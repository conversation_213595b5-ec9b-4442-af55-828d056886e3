name: Build

on:
  push:
    branches:
      - main
    paths:
      - src/**
      - package.json
      - yarn.lock
      - Dockerfile

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Enable corepack
        run: corepack enable

      - name: Install dependencies
        run: yarn install

      - name: Build TypeScript
        run: yarn build
