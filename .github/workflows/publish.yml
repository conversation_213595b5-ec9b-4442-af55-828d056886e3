name: <PERSON>uild and Push Docker Image

on:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: rmcc13/postgres-db-crd

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract tag version
        id: extract_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.extract_version.outputs.VERSION }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      # Nice to have but keeps failing
      # - name: Create GitHub Release
      #   uses: softprops/action-gh-release@v1
      #   with:
      #     tag_name: ${{ steps.extract_version.outputs.VERSION }}
      #     name: Release ${{ steps.extract_version.outputs.VERSION }}
      #     draft: false
      #     prerelease: false
      #     generate_release_notes: true
      #     files: |
      #       resource/*.yml
