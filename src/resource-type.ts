import { KubernetesObject } from '@kubernetes/client-node';

export type PgResourceStatus = {
  phase: 'Pending' | 'Ready' | 'Failed';
  message: string;
  lastUpdated: string;
  observedGeneration: number
};

export type PgResourceSpec = {
  name: string;
  username: string;
  passwordPolicy?: { length: number };
  secret: {
    name: string;
    namespace: string;
  }
}

export interface PgObject extends KubernetesObject {
  spec: PgResourceSpec;
  status: PgResourceStatus;
}
