import { pino } from 'pino';
import { requestContext } from './context';

export const createLogger = (context: string) => {
  return pino({
    level: 'trace',
    formatters: {
      level: label => ({ level: label }),
      bindings: b => {
        const correlationId = requestContext.getStore()?.correlationId;
        return {
          ...b,
          context,
          correlationId
        }
      }
    }
  });
}
