const DEFAULT_PASSWORD_LENGTH = 32;

export function generatePassword(inputLength?: number) {
  const length = inputLength ?? DEFAULT_PASSWORD_LENGTH;

  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_()*&^%$#!~';
  const password = [];
  for (let i = 0; i < length; i++) {
    password.push(charset.charAt(Math.floor(Math.random() * charset.length)));
  }

  return password.join('');
}
