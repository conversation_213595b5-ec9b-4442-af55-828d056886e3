import Operator, { OperatorLogger, ResourceEvent, ResourceEventType } from "@dot-i/k8s-operator";
import { pino } from 'pino';
import { PgObject } from "./resource-type";
import { loadConfig } from "./config";
import { requestContext, RequestContext } from './context';
import { createLogger } from "./logger";
import { generatePassword } from "./password";
import { createDatabase } from "./db";

class PgOperatorLogger implements OperatorLogger {
  constructor(private readonly logger: pino.Logger) { }

  info(message: string) {
    this.logger.info(message);
  }

  debug(message: string) {
    this.logger.debug(message);
  }

  warn(message: string) {
    this.logger.warn(message);
  }

  error(message: string) {
    this.logger.error(message);
  }
}

export class PgOperator extends Operator {

  private readonly appLogger: pino.Logger;
  private readonly config = loadConfig();

  constructor() {
    const logger = createLogger(PgOperator.name);
    super(new PgOperatorLogger(logger));
    this.appLogger = logger;
  }

  protected async init() {
    this.watchResource(
      'seabury.app',
      'v1',
      'pgs',
      async ev => {
        const obj = ev.object as PgObject;
        const password = generatePassword(obj.spec.passwordPolicy?.length);

        const context = new RequestContext(createLogger(RequestContext.name), this.config, {
          username: obj.spec.username,
          password,
          database: obj.spec.name
        });
        try {
          await requestContext.run(context, () => this.handleEvent(ev))
        } finally {
          await context.destroy();
        }
      }
    )
  }

  private async handleEvent(ev: ResourceEvent) {
    this.appLogger.trace(ev, 'Incoming event');

    if (ev.type === ResourceEventType.Added || ev.type === ResourceEventType.Modified) {
      await this.handleResourceModified(ev);
    }
  }

  private async handleResourceModified(ev: ResourceEvent) {
    const obj = ev.object as PgObject;
    const metadata = obj.metadata;

    if (obj.status && obj.status.observedGeneration === metadata?.generation) {
      this.appLogger.debug({
        observedGeneration: obj.status.observedGeneration,
        resourceGeneration: metadata.generateName
      }, 'Ignoring modification because resource spec has not changed, only status has changed');

      return;
    }

    await this.setResourceStatus(ev.meta, {
      phase: 'Pending',
      message: 'Pending resource request',
      observedGeneration: metadata?.generation,
      lastUpdated: new Date().toISOString()
    });

    if (ev.type !== ResourceEventType.Added) {
      this.appLogger.debug(ev, 'Skipping modified action, modifying resource not yet supported');
      await this.setResourceStatus(ev.meta, {
        phase: 'Ready',
        message: 'Modification not yet supported, no changes applied',
        observedGeneration: metadata?.generation,
        lastUpdated: new Date().toISOString()
      });

      return;
    }

    this.appLogger.debug(ev, 'Handling new postgres database');

    await createDatabase();

    await this.patchResourceStatus(ev.meta, {
      phase: 'Ready',
      message: 'Resource created',
      observedGeneration: metadata?.generation,
      lastUpdated: new Date().toISOString()
    });
  }
}
