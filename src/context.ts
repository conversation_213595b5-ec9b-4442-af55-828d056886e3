import { AsyncLocalStorage } from "node:async_hooks";
import { Client } from "pg";
import { v4 as uuidv4 } from 'uuid';
import { Config } from "./config";
import { pino } from 'pino';

export class RequestContext {
  private _adminClient?: Client;

  public readonly correlationId = uuidv4();
  public readonly targetDatabaseUser: string;
  public readonly targetDatabasePassword: string;
  public readonly targetDatabaseName: string;

  constructor(private readonly logger: pino.Logger, private readonly config: Config, targetDatabase: { username: string, password: string, database: string }) {
    this.targetDatabaseUser = targetDatabase.username;
    this.targetDatabasePassword = targetDatabase.password;
    this.targetDatabaseName = targetDatabase.database;
  }

  public get adminClient(): Client {
    if (!this._adminClient) {
      throw new Error('Please initialize client before accessing adminClient');
    }

    return this._adminClient;
  }

  async initClient() {
    if (!this._adminClient) {
      const config = this.config.postgres;
      this._adminClient = new Client({
        host: config.host,
        port: config.port,
        user: config.username,
        password: config.password,
        database: config.database
      });
    }

    await this._adminClient.connect();
    return this._adminClient;
  }

  async destroy() {
    try {
      await this._adminClient?.end();
    } catch (e) {
      this.logger.error({ error: e, correlationId: this.correlationId }, 'Failed to close admin PG client');
    } finally {}
  }
}

export const requestContext = new AsyncLocalStorage<RequestContext>();

export const getStore = () => {
  const store = requestContext.getStore();
  if (!store) {
    throw new Error('Store not available, function must be running inside async context to retrieve store');
  }

  return store;
}
