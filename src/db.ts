import { getStore, RequestContext } from './context';
import { createLogger } from './logger';

const logger = createLogger('Database');

async function databaseExists(context: RequestContext) {
  const result = await context.adminClient.query(
    'SELECT 1 FROM pg_database WHERE datname = $1;',
    [context.targetDatabaseName]
  );

  return result.rows.length > 1;
}

export async function createDatabase() {
  const store = getStore();
  await store.initClient();

  logger.trace({ database: store.targetDatabaseName }, 'Checking if database exists');
  const exists = await databaseExists(store);
  if (exists) {
    logger.debug({ database: store.targetDatabaseName }, 'Database already exists, skipping creation');
    return;
  }

  logger.debug({ database: store.targetDatabaseName }, 'Database does not exist, creating it now');
  await store.adminClient.query(`CREATE DATABASE "${store.targetDatabaseName}"`);
}
