{"version": "0.2.0", "configurations": [{"name": "Debug index.ts", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/index.ts", "runtimeExecutable": "node", "runtimeArgs": ["--loader", "tsx/esm"], "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}, {"name": "Debug index.ts (with tsx)", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/tsx/dist/cli.mjs", "args": ["${workspaceFolder}/src/index.ts"], "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}]}