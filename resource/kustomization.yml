apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - namespace.yml
  - crd.yml
  - service-account.yml
  - cluster-role.yml
  - deployment.yml

images:
  - name: ghcr.io/rmcc13/postgres-db-crd
    newTag: v0.4.0

secretGenerator:
  - name: postgres-connection-info
    namespace: postgres-operator
    files:
      - secrets/config.json
  - name: ghcr-auth
    namespace: postgres-operator
    files:
      - .dockerconfigjson=secrets/docker-auth.json
    type: kubernetes.io/dockerconfigjson
