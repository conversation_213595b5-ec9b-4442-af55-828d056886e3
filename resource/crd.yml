apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: pgs.seabury.app
spec:
  group: seabury.app
  scope: Namespaced
  names:
    plural: pgs
    singular: pg
    kind: PostgresDb
    shortNames:
      - pg
      - pgdb
  versions:
    - name: v1
      served: true
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            spec:
              type: object
              required:
                - name
                - username
                - secret
              properties:
                name:
                  type: string
                  description: Name of the database to create
                username:
                  type: string
                  description: Name of the user to create that will have access to the database
                passwordPolicy:
                  type: object
                  description: Rules that should be followed when creating the password
                  required:
                    - length
                  properties:
                    length:
                      type: number
                      description: Password length
                secret:
                  type: object
                  description: Details about the secret that will be created and store the database connection info
                  required:
                    - name
                    - namespace
                  properties:
                    name:
                      type: string
                      description: Name of the secret
                    namespace:
                      type: string
                      description: Namespace the secret will be created in
            status:
              type: object
              properties:
                phase:
                  type: string
                  enum: ['Pending', 'Ready', 'Failed']
                  default: 'Pending'
                message:
                  type: string
                lastUpdated:
                  type: string
                  format: date-time
                observedGeneration:
                  type: number
      subresources:
        status: {}
      additionalPrinterColumns:
        - name: Database
          type: string
          jsonPath: .spec.name
        - name: User
          type: string
          jsonPath: .spec.username
        - name: Status
          type: string
          jsonPath: .status.phase
        - name: Age
          type: date
          jsonPath: .metadata.creationTimestamp
        - name: Observed Generation
          type: number
          jsonPath: .status.observedGeneration
