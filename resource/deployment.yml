apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-operator
  namespace: postgres-operator
  labels:
    app: postgres-operator
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres-operator
  template:
    metadata:
      labels:
        app: postgres-operator
    spec:
      serviceAccountName: postgres-operator
      imagePullSecrets:
        - name: ghcr-auth
      containers:
        - name: controller
          image: ghcr.io/rmcc13/postgres-db-crd:latest
          env:
            - name: NODE_CONFIG_DIR
              value: /etc/app/config
          volumeMounts:
            - name: connection-info
              mountPath: /etc/app/config
              readOnly: true
          resources:
            requests:
              memory: 128Mi
              cpu: 100m
            limits:
              memory: 256Mi
              cpu: 200m
      volumes:
        - name: connection-info
          secret:
            secretName: postgres-connection-info
            items:
              - key: config.json
                path: default.json
