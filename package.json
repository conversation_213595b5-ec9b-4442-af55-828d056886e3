{"name": "postgres-db-crd", "packageManager": "yarn@4.9.2", "version": "0.5.0", "scripts": {"build": "tsc"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@tsconfig/strictest": "^2.0.5", "@types/config": "^3.3.5", "@types/node": "^24.1.0", "@types/pg": "^8", "tsx": "^4.20.3", "typescript": "^5.9.2"}, "dependencies": {"@dot-i/k8s-operator": "^3.1.0", "@kubernetes/client-node": "^1.3.0", "config": "^4.1.0", "pg": "^8.16.3", "pino": "^9.7.0", "uuid": "^11.1.0", "zod": "^4.0.14"}}